import 'dart:convert';
import 'dart:typed_data';
import 'package:share_plus/share_plus.dart';
import '../models/debtor.dart';

/// معلومات QR للمدين
class DebtorQRInfo {
  final String id;
  final String name;
  final double totalDebt;
  final DateTime? dueDate;
  final String notes;

  DebtorQRInfo({
    required this.id,
    required this.name,
    required this.totalDebt,
    this.dueDate,
    required this.notes,
  });

  factory DebtorQRInfo.fromJson(Map<String, dynamic> json) {
    return DebtorQRInfo(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      totalDebt: (json['totalDebt'] ?? 0.0).toDouble(),
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      notes: json['notes'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'totalDebt': totalDebt,
      'dueDate': dueDate?.toIso8601String(),
      'notes': notes,
    };
  }
}

/// معلومات QR للملخص
class SummaryQRInfo {
  final int totalDebtors;
  final double totalAmount;
  final int overdueCount;
  final DateTime generatedAt;

  SummaryQRInfo({
    required this.totalDebtors,
    required this.totalAmount,
    required this.overdueCount,
    required this.generatedAt,
  });

  factory SummaryQRInfo.fromJson(Map<String, dynamic> json) {
    return SummaryQRInfo(
      totalDebtors: json['totalDebtors'] ?? 0,
      totalAmount: (json['totalAmount'] ?? 0.0).toDouble(),
      overdueCount: json['overdueCount'] ?? 0,
      generatedAt: DateTime.parse(json['generatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalDebtors': totalDebtors,
      'totalAmount': totalAmount,
      'overdueCount': overdueCount,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }
}

/// خدمة QR الذكية
class SmartQRService {
  static const String _debtorPrefix = 'DEBTOR:';
  static const String _summaryPrefix = 'SUMMARY:';

  /// إنشاء QR للمدين
  static String generateDebtorQR(Debtor debtor) {
    final info = DebtorQRInfo(
      id: debtor.id,
      name: debtor.name,
      totalDebt: debtor.totalDebt,
      dueDate: debtor.dueDate,
      notes: debtor.notes ?? '',
    );

    final jsonData = jsonEncode(info.toJson());
    return '$_debtorPrefix$jsonData';
  }

  /// إنشاء QR للملخص
  static String generateSummaryQR({
    required int totalDebtors,
    required double totalAmount,
    required int overdueCount,
  }) {
    final info = SummaryQRInfo(
      totalDebtors: totalDebtors,
      totalAmount: totalAmount,
      overdueCount: overdueCount,
      generatedAt: DateTime.now(),
    );

    final jsonData = jsonEncode(info.toJson());
    return '$_summaryPrefix$jsonData';
  }

  /// استخراج معلومات المدين من QR
  static DebtorQRInfo? extractDebtorInfo(String qrData) {
    try {
      if (!qrData.startsWith(_debtorPrefix)) return null;

      final jsonString = qrData.substring(_debtorPrefix.length);
      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;

      return DebtorQRInfo.fromJson(jsonData);
    } catch (e) {
      return null;
    }
  }

  /// استخراج معلومات الملخص من QR
  static SummaryQRInfo? extractSummaryInfo(String qrData) {
    try {
      if (!qrData.startsWith(_summaryPrefix)) return null;

      final jsonString = qrData.substring(_summaryPrefix.length);
      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;

      return SummaryQRInfo.fromJson(jsonData);
    } catch (e) {
      return null;
    }
  }

  /// تحديد نوع QR
  static String getQRType(String qrData) {
    if (qrData.startsWith(_debtorPrefix)) {
      return 'معلومات مدين';
    } else if (qrData.startsWith(_summaryPrefix)) {
      return 'ملخص إحصائيات';
    } else {
      return 'نص عادي';
    }
  }

  /// التحقق من صحة QR
  static bool isValidQR(String qrData) {
    return extractDebtorInfo(qrData) != null ||
        extractSummaryInfo(qrData) != null ||
        qrData.isNotEmpty;
  }

  /// إنشاء صورة QR
  static Future<Uint8List> generateQRImage({
    required String data,
    int size = 300,
  }) async {
    // This is a placeholder implementation
    // In a real app, you would use a QR code generation library
    // For now, we'll return empty bytes
    return Uint8List(0);
  }

  /// مشاركة صورة QR
  static Future<void> shareQRImage({
    required Uint8List imageBytes,
    required String fileName,
    String? text,
  }) async {
    // This is a placeholder implementation
    // In a real app, you would save the image and share it
    if (text != null) {
      await Share.share(text, subject: fileName);
    }
  }
}
