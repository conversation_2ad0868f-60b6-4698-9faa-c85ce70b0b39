import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/professional_qr_service.dart';
import '../utils/design_system.dart';
import '../widgets/common_widgets.dart' as common_widgets;
import '../widgets/qr_scanner_overlay.dart';

/// شاشة قارئ QR احترافية
class ProfessionalQRScannerScreen extends StatefulWidget {
  const ProfessionalQRScannerScreen({super.key});

  @override
  State<ProfessionalQRScannerScreen> createState() =>
      _ProfessionalQRScannerScreenState();
}

class _ProfessionalQRScannerScreenState
    extends State<ProfessionalQRScannerScreen>
    with TickerProviderStateMixin {
  MobileScannerController? controller;
  bool isScanning = true;
  bool hasPermission = false;
  bool isFlashOn = false;
  CameraFacing cameraFacing = CameraFacing.back;

  // Animation controllers
  late AnimationController _scanAnimationController;
  late AnimationController _pulseAnimationController;
  late Animation<double> _scanAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _requestPermissions();
  }

  void _initAnimations() {
    _scanAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scanAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scanAnimationController, curve: Curves.linear),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _pulseAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _scanAnimationController.repeat();
    _pulseAnimationController.repeat(reverse: true);
  }

  Future<void> _requestPermissions() async {
    try {
      final status = await Permission.camera.request();

      if (mounted) {
        setState(() {
          hasPermission = status.isGranted;
        });

        if (hasPermission) {
          _initController();
        }
      }
    } catch (e) {
      debugPrint('خطأ في طلب أذونات الكاميرا: $e');
    }
  }

  void _initController() {
    controller = MobileScannerController(
      detectionSpeed: DetectionSpeed.normal,
      facing: cameraFacing,
      torchEnabled: isFlashOn,
    );
  }

  void _handleScanResult(BarcodeCapture capture) {
    if (!isScanning) return;

    final barcode = capture.barcodes.first;
    final code = barcode.rawValue;

    if (code != null) {
      setState(() {
        isScanning = false;
      });

      HapticFeedback.mediumImpact();
      _processQRCode(code);
    }
  }

  void _processQRCode(String code) {
    final qrData = ProfessionalQRService.parseQRData(code);

    if (qrData != null) {
      final qrType = ProfessionalQRService.getQRType(qrData);
      _showQRResult(qrType, qrData);
    } else {
      _showGenericResult(code);
    }
  }

  void _showQRResult(QRType? type, Map<String, dynamic> data) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildQRResultSheet(type, data),
    );
  }

  void _showGenericResult(String code) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildGenericResultSheet(code),
    );
  }

  Widget _buildQRResultSheet(QRType? type, Map<String, dynamic> data) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: BoxDecoration(
        color: DesignSystem.getCardColor(context),
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(DesignSystem.radiusLarge),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(
              vertical: DesignSystem.spaceRegular,
            ),
            decoration: BoxDecoration(
              color: DesignSystem.getSecondaryTextColor(context),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(DesignSystem.spaceMedium),
            child: Row(
              children: [
                Icon(
                  _getQRTypeIcon(type),
                  size: DesignSystem.iconLarge,
                  color: DesignSystem.primary,
                ),
                const SizedBox(width: DesignSystem.spaceRegular),
                Expanded(
                  child: Text(
                    _getQRTypeTitle(type),
                    style: DesignSystem.titleStyle,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _resumeScanning();
                  },
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          const Divider(),

          // Content
          Expanded(child: _buildQRContent(type, data)),

          // Actions
          Padding(
            padding: const EdgeInsets.all(DesignSystem.spaceMedium),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _resumeScanning();
                    },
                    child: const Text('مسح آخر'),
                  ),
                ),
                const SizedBox(width: DesignSystem.spaceRegular),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _shareQRData(data),
                    child: const Text('مشاركة'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenericResultSheet(String code) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      decoration: BoxDecoration(
        color: DesignSystem.getCardColor(context),
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(DesignSystem.radiusLarge),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(
              vertical: DesignSystem.spaceRegular,
            ),
            decoration: BoxDecoration(
              color: DesignSystem.getSecondaryTextColor(context),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(DesignSystem.spaceMedium),
            child: Row(
              children: [
                Icon(
                  Icons.qr_code,
                  size: DesignSystem.iconLarge,
                  color: DesignSystem.primary,
                ),
                const SizedBox(width: DesignSystem.spaceRegular),
                const Expanded(
                  child: Text('نتيجة المسح', style: DesignSystem.titleStyle),
                ),
                IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _resumeScanning();
                  },
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          const Divider(),

          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(DesignSystem.spaceMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('المحتوى:', style: DesignSystem.subtitleStyle),
                  const SizedBox(height: DesignSystem.spaceSmall),
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(DesignSystem.spaceMedium),
                      decoration: BoxDecoration(
                        color: DesignSystem.getBackgroundColor(context),
                        borderRadius: BorderRadius.circular(
                          DesignSystem.radiusRegular,
                        ),
                        border: Border.all(
                          color: DesignSystem.getSecondaryTextColor(
                            context,
                          ).withValues(alpha: 0.3),
                        ),
                      ),
                      child: SingleChildScrollView(
                        child: SelectableText(
                          code,
                          style: DesignSystem.bodyStyle.copyWith(
                            fontFamily: 'monospace',
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Actions
          Padding(
            padding: const EdgeInsets.all(DesignSystem.spaceMedium),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _resumeScanning();
                    },
                    child: const Text('مسح آخر'),
                  ),
                ),
                const SizedBox(width: DesignSystem.spaceRegular),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _copyToClipboard(code),
                    child: const Text('نسخ'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _resumeScanning() {
    setState(() {
      isScanning = true;
    });
  }

  void _shareQRData(Map<String, dynamic> data) {
    // TODO: تنفيذ مشاركة البيانات
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('سيتم تنفيذ المشاركة قريباً')));
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    Navigator.pop(context);
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم نسخ المحتوى')));
    _resumeScanning();
  }

  IconData _getQRTypeIcon(QRType? type) {
    return switch (type) {
      QRType.debtor => Icons.person,
      QRType.debtorSummary => Icons.summarize,
      QRType.payment => Icons.payment,
      QRType.contact => Icons.contact_phone,
      QRType.statistics => Icons.analytics,
      QRType.backup => Icons.backup,
      null => Icons.qr_code,
    };
  }

  String _getQRTypeTitle(QRType? type) {
    return switch (type) {
      QRType.debtor => 'معلومات مدين',
      QRType.debtorSummary => 'ملخص مدين',
      QRType.payment => 'دفعة',
      QRType.contact => 'معلومات اتصال',
      QRType.statistics => 'إحصائيات',
      QRType.backup => 'نسخة احتياطية',
      null => 'QR Code',
    };
  }

  Widget _buildQRContent(QRType? type, Map<String, dynamic> data) {
    // TODO: تنفيذ عرض محتوى مختلف حسب نوع QR
    return Padding(
      padding: const EdgeInsets.all(DesignSystem.spaceMedium),
      child: SingleChildScrollView(
        child: Text(data.toString(), style: DesignSystem.bodyStyle),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera view
          if (hasPermission && controller != null)
            MobileScanner(
              controller: controller!,
              onDetect: _handleScanResult,
              errorBuilder: (context, error, child) => _buildErrorView(error),
            )
          else
            _buildPermissionView(),

          // Overlay
          _buildOverlay(),

          // Top controls
          _buildTopControls(),

          // Bottom controls
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildErrorView(MobileScannerException error) {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: DesignSystem.iconXXLarge,
              color: DesignSystem.error,
            ),
            const SizedBox(height: DesignSystem.spaceMedium),
            Text(
              'خطأ في الكاميرا',
              style: DesignSystem.titleStyle.copyWith(color: Colors.white),
            ),
            const SizedBox(height: DesignSystem.spaceSmall),
            Text(
              error.errorDetails?.message ?? 'خطأ غير معروف',
              style: DesignSystem.bodyStyle.copyWith(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: DesignSystem.spaceLarge),
            ElevatedButton(
              onPressed: () {
                controller?.dispose();
                controller = null;
                _requestPermissions();
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionView() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.camera_alt_outlined,
              size: DesignSystem.iconXXLarge,
              color: Colors.white70,
            ),
            const SizedBox(height: DesignSystem.spaceMedium),
            Text(
              'إذن الكاميرا مطلوب',
              style: DesignSystem.titleStyle.copyWith(color: Colors.white),
            ),
            const SizedBox(height: DesignSystem.spaceSmall),
            Text(
              'نحتاج إلى إذن الكاميرا لمسح رموز QR',
              style: DesignSystem.bodyStyle.copyWith(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: DesignSystem.spaceLarge),
            ElevatedButton(
              onPressed: _requestPermissions,
              child: const Text('منح الإذن'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverlay() {
    return Container(
      decoration: ShapeDecoration(
        shape: QRScannerOverlayShape(
          borderColor: DesignSystem.primary,
          borderRadius: DesignSystem.radiusLarge,
          borderLength: 30,
          borderWidth: 4,
          cutOutSize: MediaQuery.of(context).size.width * 0.7,
        ),
      ),
      child: AnimatedBuilder(
        animation: _scanAnimation,
        builder: (context, child) {
          return CustomPaint(
            painter: ScanLinePainter(
              animation: _scanAnimation,
              color: DesignSystem.primary,
            ),
            size: Size.infinite,
          );
        },
      ),
    );
  }

  Widget _buildTopControls() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceMedium),
        child: Row(
          children: [
            _buildControlButton(
              icon: Icons.arrow_back,
              onPressed: () => Navigator.pop(context),
            ),
            const Spacer(),
            Text(
              'مسح QR',
              style: DesignSystem.titleStyle.copyWith(color: Colors.white),
            ),
            const Spacer(),
            _buildControlButton(
              icon: isFlashOn ? Icons.flash_on : Icons.flash_off,
              onPressed: _toggleFlash,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(DesignSystem.spaceLarge),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlButton(
                icon: Icons.flip_camera_ios,
                onPressed: _switchCamera,
                label: 'تبديل',
              ),
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: DesignSystem.primary,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: DesignSystem.primary.withValues(alpha: 0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.qr_code_scanner,
                        color: Colors.white,
                        size: DesignSystem.iconLarge,
                      ),
                    ),
                  );
                },
              ),
              _buildControlButton(
                icon: Icons.image,
                onPressed: _pickFromGallery,
                label: 'معرض',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    String? label,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.5),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
          ),
          child: IconButton(
            onPressed: onPressed,
            icon: Icon(
              icon,
              color: Colors.white,
              size: DesignSystem.iconRegular,
            ),
          ),
        ),
        if (label != null) ...[
          const SizedBox(height: DesignSystem.spaceXSmall),
          Text(
            label,
            style: DesignSystem.captionStyle.copyWith(color: Colors.white),
          ),
        ],
      ],
    );
  }

  void _toggleFlash() {
    setState(() {
      isFlashOn = !isFlashOn;
    });
    controller?.toggleTorch();
  }

  void _switchCamera() {
    setState(() {
      cameraFacing =
          cameraFacing == CameraFacing.back
              ? CameraFacing.front
              : CameraFacing.back;
    });
    controller?.switchCamera();
  }

  void _pickFromGallery() {
    // TODO: تنفيذ اختيار صورة من المعرض
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تنفيذ اختيار من المعرض قريباً')),
    );
  }

  @override
  void dispose() {
    controller?.dispose();
    _scanAnimationController.dispose();
    _pulseAnimationController.dispose();
    super.dispose();
  }
}
